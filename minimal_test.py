import sys
import traceback

print("Starting minimal test...")

try:
    print("1. Testing basic imports...")
    import pandas as pd
    import numpy as np
    print("✓ pandas and numpy imported")
    
    print("2. Testing statsmodels...")
    from statsmodels.tsa.arima.model import ARIMA
    print("✓ statsmodels imported")
    
    print("3. Testing matplotlib...")
    import matplotlib.pyplot as plt
    print("✓ matplotlib imported")
    
    print("4. Testing our arima module...")
    sys.path.append('src')
    import arima
    print("✓ arima module imported")
    
    print("5. Testing data loading...")
    df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
    print(f"✓ Data loaded: {len(df)} rows")
    
    print("6. Testing store filtering...")
    store_data = df[(df['Store'] == 1) & (df['Open'] == 1)].sort_values('Date')
    print(f"✓ Store 1 data: {len(store_data)} rows")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    traceback.print_exc()
