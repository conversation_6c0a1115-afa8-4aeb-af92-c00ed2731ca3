#!/usr/bin/env python3
"""
Fast ARIMA Test Script with Fixed Order
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def test_multiple_stores_fast():
    """Test ARIMA analysis on multiple stores with fixed order for speed."""
    
    print("Fast Multi-Store ARIMA Analysis")
    print("=" * 50)
    
    from arima import run_arima_per_store
    
    store_ids = [1, 2, 3, 4, 5]
    results = {}
    
    # Create output directories
    os.makedirs('output/arima/plot', exist_ok=True)
    os.makedirs('output/arima/results', exist_ok=True)
    
    for store_id in store_ids:
        try:
            print(f"\nAnalyzing Store {store_id}...")
            
            # Use fixed order (2,1,2) for speed
            result = run_arima_per_store(
                store_id=store_id,
                forecast_steps=7,
                output_path=f'output/arima/results/store_{store_id}_results.json',
                auto_order=False,  # Use fixed order for speed
                order=(2, 1, 2)
            )
            
            results[store_id] = result
            
            print(f"✓ Store {store_id}: ARIMA{result['model_order']}, AIC: {result['diagnostics']['aic']:.2f}")
            print(f"  Trend: {result['trend'].upper()}, RMSE: {result['diagnostics']['rmse']:.2f}")
            
        except Exception as e:
            print(f"✗ Error analyzing Store {store_id}: {e}")
    
    # Create comparison visualization
    if results:
        create_comparison_plots(results)
        print_summary_table(results)
    
    return results

def create_comparison_plots(results):
    """Create comparison plots for multiple stores."""
    
    print(f"\nCreating comparison plots...")
    
    # Prepare data for plotting
    stores = list(results.keys())
    aics = [results[s]['diagnostics']['aic'] for s in stores]
    rmses = [results[s]['diagnostics']['rmse'] for s in stores]
    mapes = [results[s]['diagnostics']['mape'] for s in stores]
    trends = [results[s]['trend'] for s in stores]
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Multi-Store ARIMA Analysis Comparison', fontsize=16, fontweight='bold')
    
    # AIC comparison
    bars1 = axes[0,0].bar(stores, aics, color='skyblue', alpha=0.7)
    axes[0,0].set_title('AIC Comparison Across Stores')
    axes[0,0].set_xlabel('Store ID')
    axes[0,0].set_ylabel('AIC')
    axes[0,0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, aic in zip(bars1, aics):
        axes[0,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 50,
                      f'{aic:.0f}', ha='center', va='bottom', fontsize=9)
    
    # RMSE comparison
    bars2 = axes[0,1].bar(stores, rmses, color='lightcoral', alpha=0.7)
    axes[0,1].set_title('RMSE Comparison Across Stores')
    axes[0,1].set_xlabel('Store ID')
    axes[0,1].set_ylabel('RMSE')
    axes[0,1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, rmse in zip(bars2, rmses):
        axes[0,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                      f'{rmse:.0f}', ha='center', va='bottom', fontsize=9)
    
    # MAPE comparison
    bars3 = axes[1,0].bar(stores, mapes, color='lightgreen', alpha=0.7)
    axes[1,0].set_title('MAPE Comparison Across Stores')
    axes[1,0].set_xlabel('Store ID')
    axes[1,0].set_ylabel('MAPE (%)')
    axes[1,0].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, mape in zip(bars3, mapes):
        axes[1,0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                      f'{mape:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # Trend distribution
    trend_counts = {'naik': 0, 'turun': 0}
    for trend in trends:
        trend_counts[trend] += 1
    
    colors = ['green' if t == 'naik' else 'red' for t in trend_counts.keys()]
    bars4 = axes[1,1].bar(trend_counts.keys(), trend_counts.values(), color=colors, alpha=0.7)
    axes[1,1].set_title('Trend Distribution')
    axes[1,1].set_xlabel('Trend Direction')
    axes[1,1].set_ylabel('Number of Stores')
    axes[1,1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, count in zip(bars4, trend_counts.values()):
        axes[1,1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.05,
                      f'{count}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    
    # Save plot
    plot_path = 'output/arima/plot/multi_store_comparison.png'
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"✓ Comparison plot saved: {plot_path}")
    
    plt.show()

def create_forecast_plots(results):
    """Create individual forecast plots for each store."""
    
    print(f"\nCreating individual forecast plots...")
    
    for store_id, result in results.items():
        try:
            plt.figure(figsize=(12, 6))
            
            # Plot forecast
            forecast_dates = pd.to_datetime(result['forecast_dates'])
            forecast_values = result['forecast']
            lower_ci = result['forecast_lower_ci']
            upper_ci = result['forecast_upper_ci']
            
            plt.plot(forecast_dates, forecast_values, 'ro-', label='Forecast', linewidth=2, markersize=6)
            plt.fill_between(forecast_dates, lower_ci, upper_ci, alpha=0.3, color='red', label='95% Confidence Interval')
            
            # Add last actual value for context
            last_date = forecast_dates[0] - timedelta(days=1)
            last_value = result['last_actual_value']
            plt.plot([last_date], [last_value], 'bo', markersize=8, label='Last Actual Value')
            
            plt.title(f'Store {store_id} - Sales Forecast\nARIMA{result["model_order"]} | Trend: {result["trend"].upper()} | AIC: {result["diagnostics"]["aic"]:.0f}')
            plt.xlabel('Date')
            plt.ylabel('Sales')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Save plot
            plot_path = f'output/arima/plot/store_{store_id}_forecast.png'
            plt.savefig(plot_path, dpi=150, bbox_inches='tight')
            print(f"✓ Store {store_id} forecast plot saved: {plot_path}")
            
            plt.close()  # Close to save memory
            
        except Exception as e:
            print(f"✗ Error creating plot for Store {store_id}: {e}")

def print_summary_table(results):
    """Print a summary table of all results."""
    
    print(f"\n{'='*80}")
    print("MULTI-STORE ARIMA ANALYSIS SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Store':<6} {'Order':<8} {'AIC':<8} {'RMSE':<8} {'MAPE':<8} {'Trend':<6} {'Magnitude':<10}")
    print("-" * 80)
    
    for store_id, result in results.items():
        print(f"{store_id:<6} {str(result['model_order']):<8} {result['diagnostics']['aic']:<8.0f} "
              f"{result['diagnostics']['rmse']:<8.0f} {result['diagnostics']['mape']:<8.1f} "
              f"{result['trend']:<6} {result['trend_magnitude_percent']:<10.1f}")
    
    print(f"{'='*80}")
    
    # Calculate averages
    avg_aic = np.mean([r['diagnostics']['aic'] for r in results.values()])
    avg_rmse = np.mean([r['diagnostics']['rmse'] for r in results.values()])
    avg_mape = np.mean([r['diagnostics']['mape'] for r in results.values()])
    
    print(f"AVERAGES: AIC: {avg_aic:.0f}, RMSE: {avg_rmse:.0f}, MAPE: {avg_mape:.1f}%")
    
    # Count trends
    trends = [r['trend'] for r in results.values()]
    naik_count = trends.count('naik')
    turun_count = trends.count('turun')
    
    print(f"TRENDS: {naik_count} stores trending UP, {turun_count} stores trending DOWN")

if __name__ == "__main__":
    print("Fast ARIMA Multi-Store Analysis")
    print("Using fixed ARIMA(2,1,2) order for speed")
    print("=" * 50)
    
    # Run multi-store analysis
    results = test_multiple_stores_fast()
    
    if results:
        # Create individual forecast plots
        create_forecast_plots(results)
        
        # Save consolidated results
        consolidated_path = 'output/arima/results/consolidated_results.json'
        with open(consolidated_path, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n✓ Consolidated results saved: {consolidated_path}")
        
        print(f"\n{'='*50}")
        print("FAST ARIMA ANALYSIS COMPLETED!")
        print(f"{'='*50}")
        print(f"Stores analyzed: {len(results)}")
        print(f"Results saved to: output/arima/results/")
        print(f"Plots saved to: output/arima/plot/")
    else:
        print("\n✗ No results generated!")
