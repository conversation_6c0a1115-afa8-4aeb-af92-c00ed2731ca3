#!/usr/bin/env python3
"""
ARIMA Test with Historical Data Visualization

This script demonstrates the enhanced ARIMA implementation with 
historical data plotting and forecast visualization.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def test_arima_with_historical_plot():
    """Test ARIMA analysis with historical data plotting."""
    
    print("ARIMA Analysis with Historical Data Visualization")
    print("=" * 60)
    
    from arima import run_arima_per_store
    
    # Create output directories
    os.makedirs('output/arima/plot', exist_ok=True)
    os.makedirs('output/arima/results', exist_ok=True)
    
    store_ids = [1, 2, 3]  # Test multiple stores
    
    for store_id in store_ids:
        try:
            print(f"\nAnalyzing Store {store_id} with historical data plotting...")
            
            # Run ARIMA analysis with plotting enabled
            result = run_arima_per_store(
                store_id=store_id,
                forecast_steps=14,  # 2-week forecast
                output_path=f'output/arima/results/store_{store_id}_with_history.json',
                auto_order=True,    # Use optimal order selection
                create_plot=True,   # Enable plotting
                show_plot=True      # Display the plot
            )
            
            print(f"✓ Store {store_id} Analysis Complete:")
            print(f"  Model: ARIMA{result['model_order']}")
            print(f"  AIC: {result['diagnostics']['aic']:.2f}")
            print(f"  RMSE: {result['diagnostics']['rmse']:.2f}")
            print(f"  MAPE: {result['diagnostics']['mape']:.2f}%")
            print(f"  Trend: {result['trend'].upper()}")
            print(f"  Data Points: {result['data_points']}")
            print(f"  Plot saved: {result.get('plot_path', 'Not created')}")
            
            # Print forecast summary
            print(f"\n  14-Day Forecast Summary:")
            for i, (date, value, lower, upper) in enumerate(zip(
                result['forecast_dates'][:7],  # Show first 7 days
                result['forecast'][:7],
                result['forecast_lower_ci'][:7],
                result['forecast_upper_ci'][:7]
            )):
                print(f"    {date}: {value:.0f} [{lower:.0f}, {upper:.0f}]")
            print(f"    ... (and 7 more days)")
            
        except Exception as e:
            print(f"✗ Error analyzing Store {store_id}: {e}")
            import traceback
            traceback.print_exc()

def create_comparison_with_history():
    """Create a comparison plot showing multiple stores with historical data."""
    
    print(f"\nCreating multi-store comparison with historical data...")
    
    from arima import run_arima_per_store
    
    store_ids = [1, 2, 3]
    results = {}
    
    # Collect results for all stores
    for store_id in store_ids:
        try:
            result = run_arima_per_store(
                store_id=store_id,
                forecast_steps=7,
                auto_order=False,  # Use fixed order for speed
                order=(2, 1, 2),
                create_plot=False  # Don't create individual plots
            )
            results[store_id] = result
        except Exception as e:
            print(f"Error with Store {store_id}: {e}")
    
    if not results:
        print("No results to plot")
        return
    
    # Create comparison plot with historical data
    fig, axes = plt.subplots(len(results), 1, figsize=(15, 5 * len(results)))
    if len(results) == 1:
        axes = [axes]
    
    # Load historical data
    df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
    
    for i, (store_id, result) in enumerate(results.items()):
        ax = axes[i]
        
        # Get historical data for this store
        store_data = df[(df['Store'] == store_id) & (df['Open'] == 1)].sort_values('Date')
        store_data.set_index('Date', inplace=True)
        sales_series = store_data['Sales'].dropna()
        
        # Plot last 30 days of historical data
        hist_data = sales_series.tail(30)
        ax.plot(hist_data.index, hist_data.values, 'b-', label='Historical Sales', linewidth=1.5, alpha=0.8)
        
        # Plot forecast
        forecast_dates = pd.to_datetime(result['forecast_dates'])
        forecast_values = result['forecast']
        lower_ci = result['forecast_lower_ci']
        upper_ci = result['forecast_upper_ci']
        
        ax.plot(forecast_dates, forecast_values, 'r-o', label='Forecast', linewidth=2, markersize=6)
        ax.fill_between(forecast_dates, lower_ci, upper_ci, alpha=0.3, color='red', 
                       label='95% Confidence Interval')
        
        # Add connection line
        last_hist_date = hist_data.index[-1]
        last_hist_value = hist_data.iloc[-1]
        first_forecast_date = forecast_dates[0]
        first_forecast_value = forecast_values[0]
        
        ax.plot([last_hist_date, first_forecast_date], [last_hist_value, first_forecast_value], 
               'g--', alpha=0.7, linewidth=1, label='Transition')
        
        # Formatting
        ax.set_title(f'Store {store_id} - ARIMA{result["model_order"]} | '
                    f'AIC: {result["diagnostics"]["aic"]:.0f} | '
                    f'Trend: {result["trend"].upper()}', fontweight='bold')
        ax.set_xlabel('Date')
        ax.set_ylabel('Sales')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add vertical line to separate historical and forecast
        ax.axvline(x=last_hist_date, color='gray', linestyle=':', alpha=0.7)
        
        # Rotate x-axis labels
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.suptitle('Multi-Store ARIMA Forecasts with Historical Data', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save comparison plot
    comparison_path = 'output/arima/plot/multi_store_historical_comparison.png'
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f"✓ Multi-store comparison plot saved: {comparison_path}")
    
    plt.show()

def demonstrate_different_forecast_horizons():
    """Demonstrate different forecast horizons with historical data."""
    
    print(f"\nDemonstrating different forecast horizons...")
    
    from arima import run_arima_per_store
    
    store_id = 1
    horizons = [7, 14, 30]  # 1 week, 2 weeks, 1 month
    
    fig, axes = plt.subplots(1, len(horizons), figsize=(20, 6))
    
    for i, horizon in enumerate(horizons):
        print(f"  Creating {horizon}-day forecast...")
        
        result = run_arima_per_store(
            store_id=store_id,
            forecast_steps=horizon,
            auto_order=False,
            order=(2, 1, 2),
            create_plot=False
        )
        
        ax = axes[i]
        
        # Load historical data
        df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
        store_data = df[(df['Store'] == store_id) & (df['Open'] == 1)].sort_values('Date')
        store_data.set_index('Date', inplace=True)
        sales_series = store_data['Sales'].dropna()
        
        # Plot last 60 days of historical data
        hist_data = sales_series.tail(60)
        ax.plot(hist_data.index, hist_data.values, 'b-', label='Historical Sales', linewidth=1, alpha=0.7)
        
        # Plot forecast
        forecast_dates = pd.to_datetime(result['forecast_dates'])
        forecast_values = result['forecast']
        lower_ci = result['forecast_lower_ci']
        upper_ci = result['forecast_upper_ci']
        
        ax.plot(forecast_dates, forecast_values, 'r-o', label='Forecast', linewidth=2, markersize=4)
        ax.fill_between(forecast_dates, lower_ci, upper_ci, alpha=0.3, color='red')
        
        # Formatting
        ax.set_title(f'{horizon}-Day Forecast\nRMSE: {result["diagnostics"]["rmse"]:.0f}')
        ax.set_xlabel('Date')
        ax.set_ylabel('Sales')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axvline(x=hist_data.index[-1], color='gray', linestyle=':', alpha=0.7)
        
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.suptitle(f'Store {store_id} - Different Forecast Horizons', fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    # Save horizon comparison
    horizon_path = 'output/arima/plot/forecast_horizons_comparison.png'
    plt.savefig(horizon_path, dpi=300, bbox_inches='tight')
    print(f"✓ Forecast horizons comparison saved: {horizon_path}")
    
    plt.show()

if __name__ == "__main__":
    print("Enhanced ARIMA with Historical Data Visualization")
    print("=" * 60)
    print("This script demonstrates:")
    print("• Historical data + forecast plotting")
    print("• Multi-store comparison with history")
    print("• Different forecast horizons")
    print("=" * 60)
    
    # Test 1: Individual store analysis with historical plotting
    test_arima_with_historical_plot()
    
    # Test 2: Multi-store comparison with historical data
    create_comparison_with_history()
    
    # Test 3: Different forecast horizons
    demonstrate_different_forecast_horizons()
    
    print(f"\n{'='*60}")
    print("HISTORICAL DATA VISUALIZATION COMPLETED!")
    print(f"{'='*60}")
    print("Generated plots:")
    print("• Individual store plots: store_X_arima_forecast_with_history.png")
    print("• Multi-store comparison: multi_store_historical_comparison.png")
    print("• Forecast horizons: forecast_horizons_comparison.png")
    print(f"\nAll plots saved to: output/arima/plot/")
