# ARIMA Enhanced Implementation - Usage Guide

## Quick Start

### 1. Run Simple Test (Recommended First)
```bash
python simple_test_arima.py
```
**What it does:**
- Tests basic ARIMA functionality
- Analyzes Store 1 with 7-day forecast
- Creates simple forecast plot
- Saves results to `output/arima/results/test_store_1.json`
- Saves plot to `output/arima/plot/simple_forecast_plot.png`

### 2. Run Fast Multi-Store Analysis
```bash
python fast_test_arima.py
```
**What it does:**
- Analyzes 5 stores (1, 2, 3, 4, 5) with fixed ARIMA(2,1,2)
- Creates comparison plots across stores
- Generates individual forecast plots for each store
- Fast execution (~2-3 minutes)

### 3. Run Feature Demonstration
```bash
python demo_arima_features.py
```
**What it does:**
- Compares fixed vs optimal order selection
- Shows comprehensive diagnostics
- Demonstrates all enhanced features
- Creates detailed comparison visualizations

### 4. Run Comprehensive Analysis (Advanced)
```bash
python test_arima.py
```
**What it does:**
- Full comprehensive analysis with optimal order selection
- Advanced diagnostic plots (ACF, PACF, residuals)
- Multi-store comparison with detailed metrics
- Takes longer due to optimal order search

## File Structure After Running Tests

```
output/arima/
├── plot/
│   ├── simple_forecast_plot.png          # Basic forecast plot
│   ├── multi_store_comparison.png        # Store comparison charts
│   ├── store_1_forecast.png              # Individual store forecasts
│   ├── store_2_forecast.png
│   ├── store_3_forecast.png
│   ├── store_4_forecast.png
│   ├── store_5_forecast.png
│   └── fixed_vs_optimal_comparison.png   # Order comparison
├── results/
│   ├── test_store_1.json                 # Simple test results
│   ├── store_1_results.json              # Detailed store results
│   ├── store_2_results.json
│   ├── store_3_results.json
│   ├── store_4_results.json
│   ├── store_5_results.json
│   └── consolidated_results.json         # All stores combined
└── demo/
    ├── fixed_vs_optimal_comparison.png   # Demo comparison plot
    ├── store_1_fixed_order.json          # Fixed order results
    └── store_1_optimal_order.json        # Optimal order results
```

## Understanding the Output

### JSON Result Structure
```json
{
  "model": "arima_enhanced",
  "target": "sales",
  "store": 1,
  "model_order": [5, 1, 5],              // (p, d, q) parameters
  "data_points": 781,
  "forecast": [4661, 4381, 4022, ...],   // Forecast values
  "forecast_lower_ci": [3355, 2813, ...], // Lower confidence bounds
  "forecast_upper_ci": [5968, 5948, ...], // Upper confidence bounds
  "confidence_level": 95.0,
  "forecast_dates": ["2015-08-01", ...],
  "trend": "turun",                       // "naik" or "turun"
  "trend_magnitude_percent": 19.1,
  "stationarity": {
    "adf_pvalue": 0.004223,
    "adf_stationary": true,
    "kpss_pvalue": 0.1,
    "kpss_stationary": true,
    "is_stationary": true
  },
  "diagnostics": {
    "aic": 12360.28,                      // Model selection criterion
    "bic": 12411.54,
    "mae": 499.29,                        // Mean Absolute Error
    "rmse": 687.61,                       // Root Mean Square Error
    "mape": 10.80,                        // Mean Absolute Percentage Error
    "residuals_white_noise": true,        // Residual tests
    "residuals_normal": false
  },
  "last_actual_value": 5263.0,
  "forecast_period_start": "2015-08-01",
  "forecast_period_end": "2015-08-07"
}
```

### Key Metrics Explanation

**Model Selection:**
- **AIC (Akaike Information Criterion)**: Lower is better for model comparison
- **BIC (Bayesian Information Criterion)**: Penalizes complexity more than AIC
- **Model Order (p,d,q)**: AR order, differencing, MA order

**Accuracy Metrics:**
- **MAE**: Average absolute error in same units as data
- **RMSE**: Root mean square error, penalizes large errors more
- **MAPE**: Percentage error, good for comparing across different scales

**Statistical Tests:**
- **ADF Test**: Tests for unit roots (non-stationarity)
- **KPSS Test**: Tests for trend stationarity
- **Ljung-Box**: Tests if residuals are white noise
- **Jarque-Bera**: Tests if residuals are normally distributed

## Customizing the Analysis

### Using the Enhanced ARIMA Function Directly
```python
from src.arima import run_arima_per_store

# Optimal order selection (recommended)
result = run_arima_per_store(
    store_id=1,
    forecast_steps=7,
    train_path='datasets/Arima/train.csv',
    output_path='my_output.json',
    auto_order=True  # Let algorithm find best order
)

# Fixed order (faster)
result = run_arima_per_store(
    store_id=1,
    forecast_steps=14,  # 2-week forecast
    auto_order=False,
    order=(2, 1, 2)     # Specify order manually
)
```

### Using the ARIMAAnalyzer Class Directly
```python
from src.arima import ARIMAAnalyzer
import pandas as pd

# Load your data
df = pd.read_csv('your_data.csv', parse_dates=['Date'])
series = df.set_index('Date')['Sales']

# Initialize analyzer
analyzer = ARIMAAnalyzer(max_p=3, max_d=2, max_q=3)

# Preprocess data
clean_series = analyzer.preprocess_data(series)

# Check stationarity
stationarity = analyzer.check_stationarity(clean_series)

# Fit model
fitted_model = analyzer.fit_model(clean_series)

# Generate diagnostics
diagnostics = analyzer.generate_diagnostics(clean_series)

# Forecast with confidence intervals
forecast = analyzer.forecast_with_intervals(steps=7)
```

## Troubleshooting

### Common Issues:

1. **"Insufficient data" error**
   - Ensure store has at least 30 data points
   - Check that store ID exists in dataset

2. **"Module not found" error**
   - Make sure you're running from the project root directory
   - Check that `src/` directory is in the same folder

3. **Slow performance with optimal order**
   - Use `auto_order=False` with fixed order for faster results
   - Reduce search space by setting smaller max_p, max_q values

4. **JSON serialization errors**
   - This should be fixed in the enhanced version
   - If still occurring, check for numpy/pandas objects in output

### Performance Tips:

- **For quick testing**: Use `simple_test_arima.py` or `fast_test_arima.py`
- **For production**: Use optimal order selection for best accuracy
- **For multiple stores**: Consider parallel processing for large datasets
- **For large datasets**: Implement data sampling or chunking

## Next Steps

1. **Run the simple test** to verify everything works
2. **Examine the generated plots** in `output/arima/plot/`
3. **Review the JSON results** for detailed metrics
4. **Try different stores** to see varying patterns
5. **Experiment with forecast horizons** (7, 14, 30 days)
6. **Compare fixed vs optimal orders** using the demo script

The enhanced ARIMA implementation is now ready for production use with comprehensive diagnostics, automatic optimization, and professional-quality outputs!
