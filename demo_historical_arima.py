#!/usr/bin/env python3
"""
Demo: ARIMA with Historical Data Visualization

This script demonstrates how to use the enhanced ARIMA implementation
to generate historical charts with forecast predictions.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def demo_basic_historical_plot():
    """Demonstrate basic ARIMA analysis with historical plotting."""
    
    print("DEMO 1: Basic ARIMA with Historical Data Plot")
    print("=" * 50)
    
    from arima import run_arima_per_store
    
    # Basic usage with historical plotting
    result = run_arima_per_store(
        store_id=1,
        forecast_steps=7,
        create_plot=True,    # Enable historical + forecast plotting
        show_plot=False,     # Don't display (just save)
        auto_order=False,    # Use fixed order for speed
        order=(2, 1, 2)
    )
    
    print(f"✓ Analysis complete for Store {result['store']}")
    print(f"  Model: ARIMA{result['model_order']}")
    print(f"  Historical plot saved: {result['plot_path']}")
    print(f"  Forecast trend: {result['trend'].upper()}")
    
    return result

def demo_optimal_order_with_plot():
    """Demonstrate optimal order selection with historical plotting."""
    
    print("\nDEMO 2: Optimal Order Selection with Historical Plot")
    print("=" * 50)
    
    from arima import run_arima_per_store
    
    print("Running optimal order selection (this may take a moment)...")
    
    # Optimal order selection with plotting
    result = run_arima_per_store(
        store_id=1,
        forecast_steps=14,   # 2-week forecast
        create_plot=True,    # Enable plotting
        show_plot=False,     # Don't display
        auto_order=True,     # Enable optimal order selection
        output_path='output/arima/results/store_1_optimal_with_plot.json'
    )
    
    print(f"✓ Optimal analysis complete")
    print(f"  Optimal Model: ARIMA{result['model_order']}")
    print(f"  AIC: {result['diagnostics']['aic']:.2f}")
    print(f"  RMSE: {result['diagnostics']['rmse']:.2f}")
    print(f"  Historical plot saved: {result['plot_path']}")
    
    return result

def demo_custom_plotting():
    """Demonstrate custom plotting using the plotting function directly."""
    
    print("\nDEMO 3: Custom Historical Plotting")
    print("=" * 50)
    
    from arima import ARIMAAnalyzer, create_arima_plot
    
    # Load data manually
    df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
    store_data = df[(df['Store'] == 1) & (df['Open'] == 1)].sort_values('Date')
    store_data.set_index('Date', inplace=True)
    sales_series = store_data['Sales'].dropna()
    
    print(f"Loaded {len(sales_series)} data points for Store 1")
    
    # Initialize analyzer and fit model
    analyzer = ARIMAAnalyzer()
    sales_clean = analyzer.preprocess_data(sales_series)
    fitted_model = analyzer.fit_model(sales_clean, order=(2, 1, 2))
    diagnostics = analyzer.generate_diagnostics(sales_clean)
    forecast_info = analyzer.forecast_with_intervals(10)  # 10-day forecast
    
    # Generate future dates
    last_date = sales_clean.index[-1]
    future_dates = [last_date + timedelta(days=i+1) for i in range(10)]
    
    # Create custom plot
    plot_path = create_arima_plot(
        sales_series=sales_clean,
        forecast_info=forecast_info,
        forecast_dates=future_dates,
        store_id=1,
        model_order=(2, 1, 2),
        diagnostics=diagnostics,
        output_dir='output/arima/plot',
        show_plot=False
    )
    
    print(f"✓ Custom plot created: {plot_path}")
    
    return plot_path

def demo_multiple_stores_with_history():
    """Demonstrate multiple stores analysis with historical plots."""
    
    print("\nDEMO 4: Multiple Stores with Historical Plots")
    print("=" * 50)
    
    from arima import run_arima_per_store
    
    store_ids = [1, 2, 3]
    results = {}
    
    for store_id in store_ids:
        print(f"  Analyzing Store {store_id}...")
        
        try:
            result = run_arima_per_store(
                store_id=store_id,
                forecast_steps=7,
                create_plot=True,
                show_plot=False,
                auto_order=False,
                order=(2, 1, 2),
                output_path=f'output/arima/results/demo_store_{store_id}.json'
            )
            
            results[store_id] = result
            print(f"    ✓ RMSE: {result['diagnostics']['rmse']:.0f}, Trend: {result['trend'].upper()}")
            print(f"    ✓ Plot: {result['plot_path']}")
            
        except Exception as e:
            print(f"    ✗ Error: {e}")
    
    print(f"\n✓ Analyzed {len(results)} stores successfully")
    return results

def create_summary_report(results):
    """Create a summary report of all analyses."""
    
    print("\nDEMO 5: Summary Report Generation")
    print("=" * 50)
    
    # Create summary data
    summary_data = []
    for store_id, result in results.items():
        summary_data.append({
            'Store': store_id,
            'Model': f"ARIMA{result['model_order']}",
            'AIC': result['diagnostics']['aic'],
            'RMSE': result['diagnostics']['rmse'],
            'MAPE': result['diagnostics']['mape'],
            'Trend': result['trend'],
            'Data_Points': result['data_points'],
            'Plot_File': os.path.basename(result['plot_path']) if result.get('plot_path') else 'None'
        })
    
    # Create summary DataFrame
    df_summary = pd.DataFrame(summary_data)
    
    # Save summary to CSV
    summary_path = 'output/arima/results/analysis_summary.csv'
    df_summary.to_csv(summary_path, index=False)
    
    # Print summary table
    print("Analysis Summary:")
    print(df_summary.to_string(index=False))
    
    print(f"\n✓ Summary saved to: {summary_path}")
    
    # Create summary plot
    fig, axes = plt.subplots(1, 3, figsize=(18, 5))
    
    # AIC comparison
    axes[0].bar(df_summary['Store'], df_summary['AIC'], color='skyblue', alpha=0.7)
    axes[0].set_title('AIC Comparison')
    axes[0].set_xlabel('Store')
    axes[0].set_ylabel('AIC')
    axes[0].grid(True, alpha=0.3)
    
    # RMSE comparison
    axes[1].bar(df_summary['Store'], df_summary['RMSE'], color='lightcoral', alpha=0.7)
    axes[1].set_title('RMSE Comparison')
    axes[1].set_xlabel('Store')
    axes[1].set_ylabel('RMSE')
    axes[1].grid(True, alpha=0.3)
    
    # Trend distribution
    trend_counts = df_summary['Trend'].value_counts()
    colors = ['green' if t == 'naik' else 'red' for t in trend_counts.index]
    axes[2].bar(trend_counts.index, trend_counts.values, color=colors, alpha=0.7)
    axes[2].set_title('Trend Distribution')
    axes[2].set_xlabel('Trend')
    axes[2].set_ylabel('Count')
    axes[2].grid(True, alpha=0.3)
    
    plt.suptitle('ARIMA Analysis Summary', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    summary_plot_path = 'output/arima/plot/analysis_summary.png'
    plt.savefig(summary_plot_path, dpi=300, bbox_inches='tight')
    print(f"✓ Summary plot saved: {summary_plot_path}")
    
    plt.show()

if __name__ == "__main__":
    print("Enhanced ARIMA with Historical Data - Complete Demo")
    print("=" * 60)
    print("This demo shows how to:")
    print("• Generate ARIMA forecasts with historical data plots")
    print("• Use both fixed and optimal order selection")
    print("• Create custom plots using the plotting function")
    print("• Analyze multiple stores")
    print("• Generate summary reports")
    print("=" * 60)
    
    # Create output directories
    os.makedirs('output/arima/plot', exist_ok=True)
    os.makedirs('output/arima/results', exist_ok=True)
    
    # Demo 1: Basic historical plotting
    basic_result = demo_basic_historical_plot()
    
    # Demo 2: Optimal order with plotting (commented out for speed)
    # optimal_result = demo_optimal_order_with_plot()
    
    # Demo 3: Custom plotting
    custom_plot = demo_custom_plotting()
    
    # Demo 4: Multiple stores
    multi_results = demo_multiple_stores_with_history()
    
    # Demo 5: Summary report
    if multi_results:
        create_summary_report(multi_results)
    
    print(f"\n{'='*60}")
    print("COMPLETE DEMO FINISHED!")
    print(f"{'='*60}")
    print("Generated files:")
    print("📊 Historical + Forecast Plots:")
    print("   • store_X_arima_forecast_with_history.png")
    print("📈 Summary Plots:")
    print("   • analysis_summary.png")
    print("📄 Results:")
    print("   • Individual store JSON files")
    print("   • analysis_summary.csv")
    print(f"\n📁 All files saved to: output/arima/")
    print("\n🎯 Key Features Demonstrated:")
    print("✓ Historical data visualization with forecasts")
    print("✓ Confidence intervals for uncertainty quantification")
    print("✓ Multiple time series analysis")
    print("✓ Comprehensive model diagnostics")
    print("✓ Professional-quality plots and reports")
    
    print(f"\n💡 Usage Tips:")
    print("• Set create_plot=True to generate historical charts")
    print("• Use auto_order=True for best accuracy (slower)")
    print("• Use auto_order=False with fixed order for speed")
    print("• Check the plot_path in results for saved chart location")
