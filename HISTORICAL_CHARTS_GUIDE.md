# ARIMA Historical Charts with Forecast - Complete Guide

## 📊 Overview

The enhanced ARIMA implementation now includes **comprehensive historical data visualization** with forecast predictions. The charts show both historical sales data and future forecasts with confidence intervals, all saved to the `output/arima/plot/` directory.

## 🎯 Key Features of Historical Charts

### 1. **Dual-Panel Layout**
- **Top Panel**: Focused view (last 60 days + forecast)
- **Bottom Panel**: Complete historical data overview

### 2. **Visual Elements**
- **Blue Line**: Historical sales data
- **Red Line with Markers**: Forecast predictions
- **Red Shaded Area**: 95% confidence intervals
- **Green Dashed Line**: Transition from historical to forecast
- **Gray Vertical Line**: Forecast start point
- **Statistics Box**: Model performance metrics

### 3. **Generated Files**
- `store_X_arima_forecast_with_history.png` - Individual store charts
- `quick_historical_comparison.png` - Multi-store comparison
- `analysis_summary.png` - Performance summary charts

## 🚀 How to Generate Historical Charts

### Method 1: Using Enhanced run_arima_per_store Function

```python
from src.arima import run_arima_per_store

# Basic usage with historical plotting
result = run_arima_per_store(
    store_id=1,
    forecast_steps=7,
    create_plot=True,    # Enable historical chart generation
    show_plot=False,     # Set to True to display chart
    auto_order=True      # Use optimal order selection
)

# Chart saved to: result['plot_path']
print(f"Historical chart saved: {result['plot_path']}")
```

### Method 2: Using Direct Plotting Function

```python
from src.arima import create_arima_plot, ARIMAAnalyzer
import pandas as pd

# Load and prepare data
df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'])
store_data = df[(df['Store'] == 1) & (df['Open'] == 1)].sort_values('Date')
sales_series = store_data.set_index('Date')['Sales']

# Fit ARIMA model
analyzer = ARIMAAnalyzer()
fitted_model = analyzer.fit_model(sales_series, order=(2, 1, 2))
forecast_info = analyzer.forecast_with_intervals(7)

# Create custom historical chart
plot_path = create_arima_plot(
    sales_series=sales_series,
    forecast_info=forecast_info,
    forecast_dates=future_dates,
    store_id=1,
    model_order=(2, 1, 2),
    diagnostics=diagnostics
)
```

## 📈 Chart Components Explained

### Historical Data Section
- **Time Range**: Shows last 60 days for detailed view
- **Data Points**: Daily sales values
- **Trend Visualization**: Clear pattern identification

### Forecast Section
- **Prediction Points**: Future sales predictions
- **Confidence Bands**: Uncertainty quantification
- **Trend Direction**: Visual trend continuation

### Statistics Panel
- **Model Information**: ARIMA order, AIC, RMSE, MAPE
- **Data Summary**: Number of data points analyzed
- **Forecast Period**: Duration and dates covered

### Overview Panel
- **Complete History**: Full dataset visualization
- **Zoom Indicator**: Highlighted region shown in detail above
- **Forecast Point**: Clear marking of prediction start

## 🎨 Available Test Scripts

### 1. `quick_historical_test.py` ⚡
**Best for**: Quick demonstration
```bash
python quick_historical_test.py
```
- **Speed**: Fast (uses fixed ARIMA order)
- **Output**: Individual + comparison charts
- **Features**: Last 30 days + 7-day forecast

### 2. `test_arima_with_history.py` 🔍
**Best for**: Comprehensive analysis
```bash
python test_arima_with_history.py
```
- **Speed**: Slower (uses optimal order selection)
- **Output**: Detailed individual charts
- **Features**: Multiple forecast horizons

### 3. `demo_historical_arima.py` 📚
**Best for**: Learning and examples
```bash
python demo_historical_arima.py
```
- **Speed**: Medium
- **Output**: Multiple demonstration charts
- **Features**: Complete feature showcase

## 📊 Generated Chart Types

### Individual Store Charts
**Filename**: `store_X_arima_forecast_with_history.png`
- Historical data (last 60 days)
- 7-14 day forecasts with confidence intervals
- Model statistics and performance metrics
- Complete data overview panel

### Multi-Store Comparisons
**Filename**: `quick_historical_comparison.png`
- Side-by-side store comparisons
- Consistent time scales for comparison
- Individual model performance metrics

### Summary Analytics
**Filename**: `analysis_summary.png`
- AIC comparison across stores
- RMSE performance metrics
- Trend distribution analysis

## 🔧 Customization Options

### Chart Appearance
```python
# Modify the create_arima_plot function parameters
plot_path = create_arima_plot(
    sales_series=sales_series,
    forecast_info=forecast_info,
    forecast_dates=future_dates,
    store_id=1,
    model_order=(2, 1, 2),
    diagnostics=diagnostics,
    output_dir='custom/path',  # Custom save location
    show_plot=True             # Display chart
)
```

### Forecast Horizons
```python
# Different forecast periods
short_term = run_arima_per_store(store_id=1, forecast_steps=7)   # 1 week
medium_term = run_arima_per_store(store_id=1, forecast_steps=14) # 2 weeks
long_term = run_arima_per_store(store_id=1, forecast_steps=30)   # 1 month
```

### Historical Data Range
The charts automatically show:
- **Detailed View**: Last 60 days + forecast
- **Overview**: Complete historical dataset
- **Transition**: Smooth connection between historical and forecast

## 📁 File Organization

```
output/arima/plot/
├── store_1_arima_forecast_with_history.png    # Individual store charts
├── store_2_arima_forecast_with_history.png
├── store_3_arima_forecast_with_history.png
├── quick_historical_comparison.png            # Multi-store comparison
├── analysis_summary.png                       # Performance summary
└── ...other charts...

output/arima/results/
├── store_1_with_history.json                  # Detailed results
├── analysis_summary.csv                       # Summary table
└── ...other results...
```

## 💡 Best Practices

### For Quick Analysis
1. Use `quick_historical_test.py` for fast results
2. Set `auto_order=False` with fixed order (2,1,2)
3. Use 7-day forecasts for immediate insights

### For Production Analysis
1. Use `auto_order=True` for optimal model selection
2. Analyze multiple stores for comparison
3. Use 14-30 day forecasts for planning

### For Presentation
1. Use `show_plot=False` to save without displaying
2. Generate summary charts for overview
3. Include confidence intervals for uncertainty communication

## 🎯 Key Benefits

✅ **Visual Clarity**: Clear separation of historical vs forecast data  
✅ **Uncertainty Quantification**: Confidence intervals show prediction reliability  
✅ **Trend Analysis**: Easy identification of sales patterns and trends  
✅ **Model Validation**: Visual assessment of model fit quality  
✅ **Professional Output**: High-resolution charts suitable for reports  
✅ **Automated Generation**: Integrated with ARIMA analysis workflow  

## 🔍 Troubleshooting

### Chart Not Generated
- Check `create_plot=True` in function call
- Verify output directory permissions
- Ensure matplotlib is properly installed

### Poor Chart Quality
- Increase DPI in `plt.savefig(dpi=300)`
- Use `bbox_inches='tight'` for better layout
- Check data quality and preprocessing

### Performance Issues
- Use `auto_order=False` for faster execution
- Reduce forecast horizon for quicker processing
- Consider data sampling for very large datasets

The enhanced ARIMA implementation now provides comprehensive historical visualization capabilities, making it easy to understand both past patterns and future predictions in a single, professional-quality chart!
