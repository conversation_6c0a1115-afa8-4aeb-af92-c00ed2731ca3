#!/usr/bin/env python3
"""
Quick ARIMA Test with Historical Data Visualization
Uses fixed order for faster execution
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def quick_test_with_history():
    """Quick test with historical data plotting using fixed order."""
    
    print("Quick ARIMA Test with Historical Data Visualization")
    print("=" * 60)
    
    from arima import run_arima_per_store
    
    # Create output directories
    os.makedirs('output/arima/plot', exist_ok=True)
    os.makedirs('output/arima/results', exist_ok=True)
    
    store_id = 1
    
    print(f"Analyzing Store {store_id} with historical data plotting...")
    print("Using fixed ARIMA(2,1,2) order for faster execution...")
    
    try:
        # Run ARIMA analysis with plotting enabled
        result = run_arima_per_store(
            store_id=store_id,
            forecast_steps=7,   # 1-week forecast
            output_path=f'output/arima/results/store_{store_id}_quick_history.json',
            auto_order=False,   # Use fixed order for speed
            order=(2, 1, 2),    # Fixed order
            create_plot=True,   # Enable plotting
            show_plot=True      # Display the plot
        )
        
        print(f"\n✓ Store {store_id} Analysis Complete:")
        print(f"  Model: ARIMA{result['model_order']}")
        print(f"  AIC: {result['diagnostics']['aic']:.2f}")
        print(f"  RMSE: {result['diagnostics']['rmse']:.2f}")
        print(f"  MAPE: {result['diagnostics']['mape']:.2f}%")
        print(f"  Trend: {result['trend'].upper()}")
        print(f"  Data Points: {result['data_points']}")
        print(f"  Plot saved: {result.get('plot_path', 'Not created')}")
        
        # Print forecast summary
        print(f"\n  7-Day Forecast Summary:")
        print(f"  {'Date':<12} {'Forecast':<10} {'Lower CI':<10} {'Upper CI':<10}")
        print("  " + "-" * 50)
        for date, value, lower, upper in zip(
            result['forecast_dates'],
            result['forecast'],
            result['forecast_lower_ci'],
            result['forecast_upper_ci']
        ):
            print(f"  {date:<12} {value:<10.0f} {lower:<10.0f} {upper:<10.0f}")
        
        return result
        
    except Exception as e:
        print(f"✗ Error analyzing Store {store_id}: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_simple_historical_comparison():
    """Create a simple comparison showing 2 stores with historical data."""
    
    print(f"\nCreating simple multi-store comparison with historical data...")
    
    from arima import run_arima_per_store
    
    store_ids = [1, 2]
    results = {}
    
    # Collect results for stores
    for store_id in store_ids:
        try:
            print(f"  Analyzing Store {store_id}...")
            result = run_arima_per_store(
                store_id=store_id,
                forecast_steps=7,
                auto_order=False,  # Use fixed order for speed
                order=(2, 1, 2),
                create_plot=False  # Don't create individual plots
            )
            results[store_id] = result
            print(f"    ✓ Store {store_id}: RMSE {result['diagnostics']['rmse']:.0f}")
        except Exception as e:
            print(f"    ✗ Error with Store {store_id}: {e}")
    
    if not results:
        print("No results to plot")
        return
    
    # Create comparison plot with historical data
    fig, axes = plt.subplots(len(results), 1, figsize=(15, 6 * len(results)))
    if len(results) == 1:
        axes = [axes]
    
    # Load historical data
    df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
    
    for i, (store_id, result) in enumerate(results.items()):
        ax = axes[i]
        
        # Get historical data for this store
        store_data = df[(df['Store'] == store_id) & (df['Open'] == 1)].sort_values('Date')
        store_data.set_index('Date', inplace=True)
        sales_series = store_data['Sales'].dropna()
        
        # Plot last 30 days of historical data
        hist_data = sales_series.tail(30)
        ax.plot(hist_data.index, hist_data.values, 'b-', label='Historical Sales (Last 30 days)', 
                linewidth=2, alpha=0.8)
        
        # Plot forecast
        forecast_dates = pd.to_datetime(result['forecast_dates'])
        forecast_values = result['forecast']
        lower_ci = result['forecast_lower_ci']
        upper_ci = result['forecast_upper_ci']
        
        ax.plot(forecast_dates, forecast_values, 'r-o', label='7-Day Forecast', 
                linewidth=2, markersize=6)
        ax.fill_between(forecast_dates, lower_ci, upper_ci, alpha=0.3, color='red', 
                       label='95% Confidence Interval')
        
        # Add connection line
        last_hist_date = hist_data.index[-1]
        last_hist_value = hist_data.iloc[-1]
        first_forecast_date = forecast_dates[0]
        first_forecast_value = forecast_values[0]
        
        ax.plot([last_hist_date, first_forecast_date], [last_hist_value, first_forecast_value], 
               'g--', alpha=0.8, linewidth=2, label='Transition')
        
        # Formatting
        ax.set_title(f'Store {store_id} - Historical Data + ARIMA{result["model_order"]} Forecast\n'
                    f'AIC: {result["diagnostics"]["aic"]:.0f} | RMSE: {result["diagnostics"]["rmse"]:.0f} | '
                    f'Trend: {result["trend"].upper()}', fontweight='bold', fontsize=12)
        ax.set_xlabel('Date', fontsize=11)
        ax.set_ylabel('Sales', fontsize=11)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # Add vertical line to separate historical and forecast
        ax.axvline(x=last_hist_date, color='gray', linestyle=':', alpha=0.8, linewidth=2, 
                  label='Forecast Start')
        
        # Add statistics box
        stats_text = f"""Last Value: {last_hist_value:.0f}
Avg Forecast: {np.mean(forecast_values):.0f}
Trend Change: {((np.mean(forecast_values) - last_hist_value) / last_hist_value * 100):+.1f}%"""
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # Rotate x-axis labels
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.suptitle('Multi-Store ARIMA Forecasts with Historical Data', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    # Save comparison plot
    comparison_path = 'output/arima/plot/quick_historical_comparison.png'
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f"✓ Quick historical comparison plot saved: {comparison_path}")
    
    plt.show()

if __name__ == "__main__":
    print("Quick ARIMA Historical Data Visualization Test")
    print("=" * 60)
    print("This script demonstrates:")
    print("• Fast ARIMA analysis with historical data plotting")
    print("• Uses fixed ARIMA(2,1,2) order for speed")
    print("• Shows last 30 days of historical data + 7-day forecast")
    print("=" * 60)
    
    # Test 1: Single store with historical plotting
    result = quick_test_with_history()
    
    if result:
        # Test 2: Multi-store comparison with historical data
        create_simple_historical_comparison()
        
        print(f"\n{'='*60}")
        print("QUICK HISTORICAL VISUALIZATION COMPLETED!")
        print(f"{'='*60}")
        print("Generated plots:")
        print("• Individual store: store_1_arima_forecast_with_history.png")
        print("• Multi-store comparison: quick_historical_comparison.png")
        print(f"\nAll plots saved to: output/arima/plot/")
        print("\nKey features demonstrated:")
        print("✓ Historical data (last 30 days) + forecast visualization")
        print("✓ Confidence intervals for forecast uncertainty")
        print("✓ Transition line connecting historical to forecast")
        print("✓ Complete historical data overview (bottom panel)")
        print("✓ Model statistics and performance metrics")
    else:
        print("\n✗ Test failed - check error messages above")
