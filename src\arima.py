import pandas as pd
import json
import os
import numpy as np
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.arima.model import ARIMAResults
from datetime import timedelta
import warnings
import logging
from typing import Tuple, Dict, List, Optional
from scipy import stats

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

class ARIMAAnalyzer:
    """Enhanced ARIMA analyzer with automatic model selection and comprehensive diagnostics."""

    def __init__(self, max_p: int = 5, max_d: int = 2, max_q: int = 5, seasonal: bool = False):
        self.max_p = max_p
        self.max_d = max_d
        self.max_q = max_q
        self.seasonal = seasonal
        self.model = None
        self.fitted_model = None
        self.diagnostics = {}

    def check_stationarity(self, series: pd.Series, alpha: float = 0.05) -> Dict:
        """Check stationarity using ADF and KPSS tests."""
        try:
            # Augmented Dickey-Fuller test
            adf_result = adfuller(series.dropna())
            adf_stationary = adf_result[1] < alpha

            # KPSS test
            kpss_result = kpss(series.dropna(), regression='c')
            kpss_stationary = kpss_result[1] > alpha

            return {
                'adf_pvalue': float(adf_result[1]),
                'adf_stationary': bool(adf_stationary),
                'kpss_pvalue': float(kpss_result[1]),
                'kpss_stationary': bool(kpss_stationary),
                'is_stationary': bool(adf_stationary and kpss_stationary)
            }
        except Exception as e:
            logger.warning(f"Stationarity test failed: {e}")
            return {'is_stationary': False, 'error': str(e)}

    def preprocess_data(self, series: pd.Series) -> pd.Series:
        """Preprocess the time series data."""
        # Remove outliers using IQR method
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        # Cap outliers instead of removing them to preserve time series structure
        series_clean = series.clip(lower=lower_bound, upper=upper_bound)

        # Handle missing values with forward fill then backward fill
        series_clean = series_clean.ffill().bfill()

        logger.info(f"Preprocessed series: {len(series)} -> {len(series_clean)} points")
        return series_clean

    def find_optimal_order(self, series: pd.Series) -> Tuple[int, int, int]:
        """Find optimal ARIMA order using AIC criterion."""
        best_aic = np.inf
        best_order = (1, 1, 1)

        logger.info("Searching for optimal ARIMA order...")

        for p in range(self.max_p + 1):
            for d in range(self.max_d + 1):
                for q in range(self.max_q + 1):
                    try:
                        model = ARIMA(series, order=(p, d, q))
                        fitted = model.fit()

                        if fitted.aic < best_aic:
                            best_aic = fitted.aic
                            best_order = (p, d, q)

                    except Exception:
                        continue

        logger.info(f"Optimal order found: {best_order} with AIC: {best_aic:.2f}")
        return best_order

    def fit_model(self, series: pd.Series, order: Optional[Tuple[int, int, int]] = None) -> ARIMAResults:
        """Fit ARIMA model with given or optimal order."""
        if order is None:
            order = self.find_optimal_order(series)

        try:
            self.model = ARIMA(series, order=order)
            self.fitted_model = self.model.fit()
            logger.info(f"ARIMA{order} model fitted successfully")
            return self.fitted_model
        except Exception as e:
            logger.error(f"Failed to fit ARIMA{order}: {e}")
            # Fallback to simple model
            self.model = ARIMA(series, order=(1, 1, 1))
            self.fitted_model = self.model.fit()
            logger.info("Fallback to ARIMA(1,1,1)")
            return self.fitted_model

    def generate_diagnostics(self, series: pd.Series) -> Dict:
        """Generate comprehensive model diagnostics."""
        if self.fitted_model is None:
            raise ValueError("Model must be fitted before generating diagnostics")

        diagnostics = {}

        # Basic model info
        diagnostics['aic'] = self.fitted_model.aic
        diagnostics['bic'] = self.fitted_model.bic
        diagnostics['hqic'] = self.fitted_model.hqic
        diagnostics['llf'] = self.fitted_model.llf

        # Residual analysis
        residuals = self.fitted_model.resid
        diagnostics['residual_mean'] = float(residuals.mean())
        diagnostics['residual_std'] = float(residuals.std())

        # Ljung-Box test for residual autocorrelation
        try:
            lb_test = acorr_ljungbox(residuals, lags=10, return_df=True)
            diagnostics['ljung_box_pvalue'] = float(lb_test['lb_pvalue'].iloc[-1])
            diagnostics['residuals_white_noise'] = bool(diagnostics['ljung_box_pvalue'] > 0.05)
        except Exception as e:
            logger.warning(f"Ljung-Box test failed: {e}")
            diagnostics['ljung_box_pvalue'] = None
            diagnostics['residuals_white_noise'] = None

        # Normality test for residuals
        try:
            _, normality_pvalue = stats.jarque_bera(residuals.dropna())
            diagnostics['residual_normality_pvalue'] = float(normality_pvalue)
            diagnostics['residuals_normal'] = bool(normality_pvalue > 0.05)
        except Exception as e:
            logger.warning(f"Normality test failed: {e}")
            diagnostics['residual_normality_pvalue'] = None
            diagnostics['residuals_normal'] = None

        # In-sample fit metrics
        fitted_values = self.fitted_model.fittedvalues
        actual_values = series.loc[fitted_values.index]

        mae = np.mean(np.abs(actual_values - fitted_values))
        rmse = np.sqrt(np.mean((actual_values - fitted_values) ** 2))
        mape = np.mean(np.abs((actual_values - fitted_values) / actual_values)) * 100

        diagnostics['mae'] = float(mae)
        diagnostics['rmse'] = float(rmse)
        diagnostics['mape'] = float(mape)

        self.diagnostics = diagnostics
        return diagnostics

    def forecast_with_intervals(self, steps: int, alpha: float = 0.05) -> Dict:
        """Generate forecasts with confidence intervals."""
        if self.fitted_model is None:
            raise ValueError("Model must be fitted before forecasting")

        # Get forecast with confidence intervals
        forecast_result = self.fitted_model.get_forecast(steps=steps)
        forecast_values = forecast_result.predicted_mean
        conf_int = forecast_result.conf_int(alpha=alpha)

        return {
            'forecast': forecast_values.tolist(),
            'lower_ci': conf_int.iloc[:, 0].tolist(),
            'upper_ci': conf_int.iloc[:, 1].tolist(),
            'confidence_level': (1 - alpha) * 100
        }


def run_arima_per_store(store_id: int = 1, forecast_steps: int = 3,
                       train_path: str = 'datasets/Arima/train.csv',
                       output_path: str = 'outputs/predictions/arima_output.json',
                       auto_order: bool = True, order: Optional[Tuple[int, int, int]] = None) -> Dict:
    """
    Enhanced ARIMA analysis for a specific store with automatic model selection and diagnostics.

    Args:
        store_id: Store ID to analyze
        forecast_steps: Number of future periods to forecast
        train_path: Path to training data
        output_path: Path to save output JSON
        auto_order: Whether to automatically select optimal ARIMA order
        order: Manual ARIMA order (p,d,q) if auto_order is False

    Returns:
        Dictionary containing forecast results and diagnostics
    """
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # Load and filter data
    logger.info(f"Loading data for Store {store_id}")
    df = pd.read_csv(train_path, parse_dates=['Date'], low_memory=False)
    df = df[(df['Store'] == store_id) & (df['Open'] == 1)].sort_values('Date')

    if len(df) < 30:  # Increased minimum data requirement
        raise ValueError(f"Insufficient data for Store {store_id}: {len(df)} points (minimum 30 required)")

    df.set_index('Date', inplace=True)
    sales_series = df['Sales'].dropna()

    logger.info(f"Analyzing {len(sales_series)} data points for Store {store_id}")

    # Initialize analyzer
    analyzer = ARIMAAnalyzer()

    # Preprocess data
    sales_clean = analyzer.preprocess_data(sales_series)

    # Check stationarity
    stationarity_info = analyzer.check_stationarity(sales_clean)

    # Fit model
    if auto_order:
        fitted_model = analyzer.fit_model(sales_clean)
        used_order = fitted_model.model.order
    else:
        if order is None:
            order = (1, 1, 1)
        fitted_model = analyzer.fit_model(sales_clean, order)
        used_order = order

    # Generate diagnostics
    diagnostics = analyzer.generate_diagnostics(sales_clean)

    # Generate forecasts with confidence intervals
    forecast_info = analyzer.forecast_with_intervals(forecast_steps)

    # Generate future dates
    last_date = df.index[-1]
    future_dates = [last_date + timedelta(days=i+1) for i in range(forecast_steps)]

    # Determine trend
    last_real = float(sales_clean.iloc[-1])
    forecast_end = forecast_info['forecast'][-1]
    trend = "naik" if forecast_end > last_real else "turun"
    trend_magnitude = abs((forecast_end - last_real) / last_real) * 100

    # Compile comprehensive output
    output = {
        "model": "arima_enhanced",
        "target": "sales",
        "store": store_id,
        "model_order": used_order,
        "data_points": len(sales_clean),
        "forecast": forecast_info['forecast'],
        "forecast_lower_ci": forecast_info['lower_ci'],
        "forecast_upper_ci": forecast_info['upper_ci'],
        "confidence_level": forecast_info['confidence_level'],
        "forecast_dates": [d.strftime('%Y-%m-%d') for d in future_dates],
        "trend": trend,
        "trend_magnitude_percent": float(trend_magnitude),
        "stationarity": stationarity_info,
        "diagnostics": diagnostics,
        "last_actual_value": last_real,
        "forecast_period_start": future_dates[0].strftime('%Y-%m-%d'),
        "forecast_period_end": future_dates[-1].strftime('%Y-%m-%d')
    }

    # Save output
    with open(output_path, 'w') as f:
        json.dump(output, f, indent=2)

    logger.info(f"Enhanced ARIMA output for Store {store_id} saved to {output_path}")
    logger.info(f"Model: ARIMA{used_order}, AIC: {diagnostics['aic']:.2f}, RMSE: {diagnostics['rmse']:.2f}")

    return output

