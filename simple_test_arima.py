#!/usr/bin/env python3
"""
Simple ARIMA Test Script
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def test_basic_arima():
    """Test basic ARIMA functionality."""
    print("Testing basic ARIMA functionality...")
    
    try:
        from arima import run_arima_per_store
        
        # Test with store 1
        print("Running ARIMA analysis for Store 1...")
        result = run_arima_per_store(
            store_id=1, 
            forecast_steps=7,
            output_path='output/arima/results/test_store_1.json'
        )
        
        print("✓ ARIMA analysis completed successfully!")
        print(f"Model: {result['model']}")
        print(f"Store: {result['store']}")
        print(f"Model Order: {result['model_order']}")
        print(f"Data Points: {result['data_points']}")
        print(f"Trend: {result['trend']}")
        print(f"AIC: {result['diagnostics']['aic']:.2f}")
        print(f"RMSE: {result['diagnostics']['rmse']:.2f}")
        
        return result
        
    except Exception as e:
        print(f"✗ Error in ARIMA analysis: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_simple_plot(result):
    """Create a simple forecast plot."""
    if result is None:
        return
        
    print("\nCreating forecast plot...")
    
    try:
        # Create output directory
        os.makedirs('output/arima/plot', exist_ok=True)
        
        # Create a simple forecast plot
        plt.figure(figsize=(12, 6))
        
        # Plot forecast values
        forecast_dates = pd.to_datetime(result['forecast_dates'])
        forecast_values = result['forecast']
        lower_ci = result['forecast_lower_ci']
        upper_ci = result['forecast_upper_ci']
        
        plt.plot(forecast_dates, forecast_values, 'ro-', label='Forecast', linewidth=2, markersize=6)
        plt.fill_between(forecast_dates, lower_ci, upper_ci, alpha=0.3, color='red', label='95% Confidence Interval')
        
        # Add last actual value for context
        last_date = forecast_dates[0] - timedelta(days=1)
        last_value = result['last_actual_value']
        plt.plot([last_date], [last_value], 'bo', markersize=8, label='Last Actual Value')
        
        plt.title(f'Store {result["store"]} - Sales Forecast\nARIMA{result["model_order"]} | Trend: {result["trend"].upper()}')
        plt.xlabel('Date')
        plt.ylabel('Sales')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Save plot
        plot_path = 'output/arima/plot/simple_forecast_plot.png'
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        print(f"✓ Plot saved to: {plot_path}")
        
        plt.show()
        
    except Exception as e:
        print(f"✗ Error creating plot: {e}")
        import traceback
        traceback.print_exc()

def print_forecast_table(result):
    """Print a formatted forecast table."""
    if result is None:
        return
        
    print(f"\n{'='*60}")
    print(f"FORECAST RESULTS FOR STORE {result['store']}")
    print(f"{'='*60}")
    print(f"Model: ARIMA{result['model_order']}")
    print(f"Last Actual Value: {result['last_actual_value']:.2f}")
    print(f"Trend: {result['trend'].upper()} ({result['trend_magnitude_percent']:.1f}%)")
    print(f"\n{'Date':<12} {'Forecast':<10} {'Lower CI':<10} {'Upper CI':<10}")
    print("-" * 50)
    
    for date, forecast, lower, upper in zip(
        result['forecast_dates'],
        result['forecast'],
        result['forecast_lower_ci'],
        result['forecast_upper_ci']
    ):
        print(f"{date:<12} {forecast:<10.0f} {lower:<10.0f} {upper:<10.0f}")
    
    print(f"\n{'='*60}")
    print("MODEL DIAGNOSTICS")
    print(f"{'='*60}")
    print(f"AIC: {result['diagnostics']['aic']:.2f}")
    print(f"BIC: {result['diagnostics']['bic']:.2f}")
    print(f"MAE: {result['diagnostics']['mae']:.2f}")
    print(f"RMSE: {result['diagnostics']['rmse']:.2f}")
    print(f"MAPE: {result['diagnostics']['mape']:.2f}%")
    print(f"Residuals White Noise: {result['diagnostics']['residuals_white_noise']}")
    print(f"Residuals Normal: {result['diagnostics']['residuals_normal']}")

if __name__ == "__main__":
    print("Simple ARIMA Test Script")
    print("=" * 40)
    
    # Test basic functionality
    result = test_basic_arima()
    
    if result:
        # Print results table
        print_forecast_table(result)
        
        # Create simple plot
        create_simple_plot(result)
        
        print(f"\n✓ Test completed successfully!")
        print(f"Results saved to: output/arima/results/")
        print(f"Plots saved to: output/arima/plot/")
    else:
        print("\n✗ Test failed!")
