#!/usr/bin/env python3
"""
ARIMA Features Demonstration Script

This script demonstrates all the enhanced features of the improved ARIMA implementation:
1. Automatic optimal order selection
2. Fixed order analysis
3. Comprehensive diagnostics
4. Confidence intervals
5. Stationarity testing
6. Model comparison
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json

# Add src directory to path
sys.path.append('src')

def demonstrate_optimal_vs_fixed():
    """Demonstrate the difference between optimal and fixed order selection."""
    
    print("ARIMA ORDER SELECTION COMPARISON")
    print("=" * 50)
    
    from arima import run_arima_per_store
    
    store_id = 1
    
    # Create output directories
    os.makedirs('output/arima/demo', exist_ok=True)
    
    print(f"\nAnalyzing Store {store_id} with different approaches...")
    
    # 1. Fixed order ARIMA(2,1,2)
    print("\n1. Fixed Order ARIMA(2,1,2):")
    result_fixed = run_arima_per_store(
        store_id=store_id,
        forecast_steps=7,
        output_path=f'output/arima/demo/store_{store_id}_fixed_order.json',
        auto_order=False,
        order=(2, 1, 2)
    )
    
    print(f"   AIC: {result_fixed['diagnostics']['aic']:.2f}")
    print(f"   RMSE: {result_fixed['diagnostics']['rmse']:.2f}")
    print(f"   MAPE: {result_fixed['diagnostics']['mape']:.2f}%")
    
    # 2. Optimal order selection
    print("\n2. Optimal Order Selection:")
    result_optimal = run_arima_per_store(
        store_id=store_id,
        forecast_steps=7,
        output_path=f'output/arima/demo/store_{store_id}_optimal_order.json',
        auto_order=True
    )
    
    print(f"   Optimal Order: ARIMA{result_optimal['model_order']}")
    print(f"   AIC: {result_optimal['diagnostics']['aic']:.2f}")
    print(f"   RMSE: {result_optimal['diagnostics']['rmse']:.2f}")
    print(f"   MAPE: {result_optimal['diagnostics']['mape']:.2f}%")
    
    # Compare results
    print(f"\n3. Comparison:")
    aic_improvement = result_fixed['diagnostics']['aic'] - result_optimal['diagnostics']['aic']
    rmse_improvement = result_fixed['diagnostics']['rmse'] - result_optimal['diagnostics']['rmse']
    
    print(f"   AIC Improvement: {aic_improvement:.2f} ({'better' if aic_improvement > 0 else 'worse'})")
    print(f"   RMSE Improvement: {rmse_improvement:.2f} ({'better' if rmse_improvement > 0 else 'worse'})")
    
    # Create comparison plot
    create_comparison_plot(result_fixed, result_optimal, store_id)
    
    return result_fixed, result_optimal

def create_comparison_plot(result_fixed, result_optimal, store_id):
    """Create a comparison plot between fixed and optimal order results."""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Store {store_id}: Fixed vs Optimal Order ARIMA Comparison', fontsize=14, fontweight='bold')
    
    # Prepare data
    forecast_dates = pd.to_datetime(result_fixed['forecast_dates'])
    
    # Plot 1: Forecasts comparison
    ax1 = axes[0, 0]
    ax1.plot(forecast_dates, result_fixed['forecast'], 'b-o', label=f'Fixed ARIMA{result_fixed["model_order"]}', linewidth=2)
    ax1.fill_between(forecast_dates, result_fixed['forecast_lower_ci'], result_fixed['forecast_upper_ci'], 
                     alpha=0.2, color='blue')
    
    ax1.plot(forecast_dates, result_optimal['forecast'], 'r-s', label=f'Optimal ARIMA{result_optimal["model_order"]}', linewidth=2)
    ax1.fill_between(forecast_dates, result_optimal['forecast_lower_ci'], result_optimal['forecast_upper_ci'], 
                     alpha=0.2, color='red')
    
    ax1.set_title('Forecast Comparison')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Sales')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # Plot 2: Model metrics comparison
    ax2 = axes[0, 1]
    metrics = ['AIC', 'RMSE', 'MAPE']
    fixed_values = [result_fixed['diagnostics']['aic'], result_fixed['diagnostics']['rmse'], result_fixed['diagnostics']['mape']]
    optimal_values = [result_optimal['diagnostics']['aic'], result_optimal['diagnostics']['rmse'], result_optimal['diagnostics']['mape']]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    # Normalize values for better visualization
    fixed_norm = [v/max(fixed_values[i], optimal_values[i]) for i, v in enumerate(fixed_values)]
    optimal_norm = [v/max(fixed_values[i], optimal_values[i]) for i, v in enumerate(optimal_values)]
    
    ax2.bar(x - width/2, fixed_norm, width, label='Fixed Order', alpha=0.7, color='blue')
    ax2.bar(x + width/2, optimal_norm, width, label='Optimal Order', alpha=0.7, color='red')
    
    ax2.set_title('Normalized Model Metrics')
    ax2.set_xlabel('Metrics')
    ax2.set_ylabel('Normalized Value')
    ax2.set_xticks(x)
    ax2.set_xticklabels(metrics)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Confidence intervals width comparison
    ax3 = axes[1, 0]
    fixed_ci_width = [u - l for u, l in zip(result_fixed['forecast_upper_ci'], result_fixed['forecast_lower_ci'])]
    optimal_ci_width = [u - l for u, l in zip(result_optimal['forecast_upper_ci'], result_optimal['forecast_lower_ci'])]
    
    ax3.plot(forecast_dates, fixed_ci_width, 'b-o', label='Fixed Order CI Width', linewidth=2)
    ax3.plot(forecast_dates, optimal_ci_width, 'r-s', label='Optimal Order CI Width', linewidth=2)
    
    ax3.set_title('Confidence Interval Width Comparison')
    ax3.set_xlabel('Date')
    ax3.set_ylabel('CI Width')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.tick_params(axis='x', rotation=45)
    
    # Plot 4: Summary statistics
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    summary_text = f"""
    COMPARISON SUMMARY
    
    Fixed Order ARIMA{result_fixed['model_order']}:
    • AIC: {result_fixed['diagnostics']['aic']:.2f}
    • RMSE: {result_fixed['diagnostics']['rmse']:.2f}
    • MAPE: {result_fixed['diagnostics']['mape']:.2f}%
    • Trend: {result_fixed['trend'].upper()}
    
    Optimal Order ARIMA{result_optimal['model_order']}:
    • AIC: {result_optimal['diagnostics']['aic']:.2f}
    • RMSE: {result_optimal['diagnostics']['rmse']:.2f}
    • MAPE: {result_optimal['diagnostics']['mape']:.2f}%
    • Trend: {result_optimal['trend'].upper()}
    
    Improvements (Optimal vs Fixed):
    • AIC: {result_fixed['diagnostics']['aic'] - result_optimal['diagnostics']['aic']:.2f}
    • RMSE: {result_fixed['diagnostics']['rmse'] - result_optimal['diagnostics']['rmse']:.2f}
    • MAPE: {result_fixed['diagnostics']['mape'] - result_optimal['diagnostics']['mape']:.2f}%
    """
    
    ax4.text(0.05, 0.95, summary_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # Save plot
    plot_path = 'output/arima/demo/fixed_vs_optimal_comparison.png'
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"\n✓ Comparison plot saved: {plot_path}")
    
    plt.show()

def demonstrate_diagnostics():
    """Demonstrate the comprehensive diagnostics features."""
    
    print(f"\n{'='*60}")
    print("COMPREHENSIVE DIAGNOSTICS DEMONSTRATION")
    print(f"{'='*60}")
    
    from arima import ARIMAAnalyzer
    
    # Load data for demonstration
    df = pd.read_csv('datasets/Arima/train.csv', parse_dates=['Date'], low_memory=False)
    store_data = df[(df['Store'] == 1) & (df['Open'] == 1)].sort_values('Date')
    store_data.set_index('Date', inplace=True)
    sales_series = store_data['Sales'].dropna()
    
    print(f"Analyzing {len(sales_series)} data points...")
    
    # Initialize analyzer
    analyzer = ARIMAAnalyzer()
    
    # 1. Stationarity testing
    print("\n1. STATIONARITY ANALYSIS:")
    stationarity = analyzer.check_stationarity(sales_series)
    print(f"   ADF p-value: {stationarity['adf_pvalue']:.6f}")
    print(f"   ADF Stationary: {stationarity['adf_stationary']}")
    print(f"   KPSS p-value: {stationarity['kpss_pvalue']:.6f}")
    print(f"   KPSS Stationary: {stationarity['kpss_stationary']}")
    print(f"   Overall Stationary: {stationarity['is_stationary']}")
    
    # 2. Data preprocessing
    print("\n2. DATA PREPROCESSING:")
    original_length = len(sales_series)
    sales_clean = analyzer.preprocess_data(sales_series)
    print(f"   Original data points: {original_length}")
    print(f"   After preprocessing: {len(sales_clean)}")
    print(f"   Outliers handled: {original_length - len(sales_clean)} points")
    
    # 3. Model fitting and diagnostics
    print("\n3. MODEL FITTING AND DIAGNOSTICS:")
    fitted_model = analyzer.fit_model(sales_clean, order=(2, 1, 2))
    diagnostics = analyzer.generate_diagnostics(sales_clean)
    
    print(f"   Model Order: ARIMA(2, 1, 2)")
    print(f"   AIC: {diagnostics['aic']:.2f}")
    print(f"   BIC: {diagnostics['bic']:.2f}")
    print(f"   Log-Likelihood: {diagnostics['llf']:.2f}")
    print(f"   MAE: {diagnostics['mae']:.2f}")
    print(f"   RMSE: {diagnostics['rmse']:.2f}")
    print(f"   MAPE: {diagnostics['mape']:.2f}%")
    print(f"   Residuals White Noise: {diagnostics['residuals_white_noise']}")
    print(f"   Residuals Normal: {diagnostics['residuals_normal']}")
    
    # 4. Forecasting with confidence intervals
    print("\n4. FORECASTING WITH CONFIDENCE INTERVALS:")
    forecast_info = analyzer.forecast_with_intervals(7)
    print(f"   Forecast steps: 7")
    print(f"   Confidence level: {forecast_info['confidence_level']}%")
    print(f"   Forecast range: {min(forecast_info['forecast']):.0f} - {max(forecast_info['forecast']):.0f}")
    print(f"   Average CI width: {np.mean([u-l for u, l in zip(forecast_info['upper_ci'], forecast_info['lower_ci'])]):.0f}")

if __name__ == "__main__":
    print("ENHANCED ARIMA IMPLEMENTATION DEMONSTRATION")
    print("=" * 60)
    print("This script demonstrates all enhanced features:")
    print("• Automatic optimal order selection")
    print("• Comprehensive diagnostics")
    print("• Stationarity testing")
    print("• Confidence intervals")
    print("• Model comparison")
    print("=" * 60)
    
    # Demonstrate optimal vs fixed order
    result_fixed, result_optimal = demonstrate_optimal_vs_fixed()
    
    # Demonstrate comprehensive diagnostics
    demonstrate_diagnostics()
    
    print(f"\n{'='*60}")
    print("DEMONSTRATION COMPLETED!")
    print(f"{'='*60}")
    print("Key improvements implemented:")
    print("✓ Automatic ARIMA order selection using AIC")
    print("✓ Data preprocessing with outlier handling")
    print("✓ Stationarity testing (ADF + KPSS)")
    print("✓ Comprehensive model diagnostics")
    print("✓ Confidence intervals for forecasts")
    print("✓ JSON serialization compatibility")
    print("✓ Enhanced error handling and logging")
    print("✓ Multiple evaluation metrics (MAE, RMSE, MAPE)")
    print("✓ Residual analysis and model validation")
    print(f"\nAll outputs saved to: output/arima/demo/")
