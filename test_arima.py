#!/usr/bin/env python3
"""
Comprehensive ARIMA Testing and Visualization Script

This script tests the enhanced ARIMA implementation and generates detailed
visualizations and reports saved to output/arima/plot/ directory.
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json
import warnings
from typing import List, Dict, Tuple

# Add src directory to path
sys.path.append('src')
from arima import run_arima_per_store, ARIMAAnalyzer

# Configure matplotlib
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10
warnings.filterwarnings("ignore")

# Create output directories
OUTPUT_DIR = "output/arima/plot"
RESULTS_DIR = "output/arima/results"
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(RESULTS_DIR, exist_ok=True)

def create_comprehensive_plots(store_id: int, data_path: str = 'datasets/Arima/train.csv') -> Dict:
    """Create comprehensive ARIMA analysis plots for a specific store."""

    print(f"\n{'='*60}")
    print(f"COMPREHENSIVE ARIMA ANALYSIS FOR STORE {store_id}")
    print(f"{'='*60}")

    # Load and prepare data
    df = pd.read_csv(data_path, parse_dates=['Date'], low_memory=False)
    store_data = df[(df['Store'] == store_id) & (df['Open'] == 1)].sort_values('Date')

    if len(store_data) < 30:
        print(f"Insufficient data for Store {store_id}: {len(store_data)} points")
        return {}

    store_data.set_index('Date', inplace=True)
    sales_series = store_data['Sales'].dropna()

    # Run enhanced ARIMA analysis
    print(f"Running enhanced ARIMA analysis...")
    arima_results = run_arima_per_store(
        store_id=store_id,
        forecast_steps=7,  # 7-day forecast
        output_path=f'{RESULTS_DIR}/store_{store_id}_arima_results.json'
    )

    # Initialize analyzer for additional diagnostics
    analyzer = ARIMAAnalyzer()
    sales_clean = analyzer.preprocess_data(sales_series)
    fitted_model = analyzer.fit_model(sales_clean)

    # Create comprehensive visualization
    fig = plt.figure(figsize=(20, 16))

    # 1. Historical Data and Forecast Plot
    ax1 = plt.subplot(3, 3, 1)

    # Plot historical data
    sales_clean.plot(ax=ax1, label='Historical Sales', color='blue', alpha=0.7)

    # Plot forecast
    last_date = sales_clean.index[-1]
    forecast_dates = [last_date + timedelta(days=i+1) for i in range(7)]
    forecast_df = pd.Series(arima_results['forecast'], index=forecast_dates)
    forecast_lower = pd.Series(arima_results['forecast_lower_ci'], index=forecast_dates)
    forecast_upper = pd.Series(arima_results['forecast_upper_ci'], index=forecast_dates)

    forecast_df.plot(ax=ax1, label='Forecast', color='red', linewidth=2)
    ax1.fill_between(forecast_dates, forecast_lower, forecast_upper,
                     alpha=0.3, color='red', label='95% Confidence Interval')

    ax1.set_title(f'Store {store_id}: Sales Forecast\nARIMA{arima_results["model_order"]}')
    ax1.set_ylabel('Sales')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Residuals Plot
    ax2 = plt.subplot(3, 3, 2)
    residuals = fitted_model.resid
    residuals.plot(ax=ax2, color='green', alpha=0.7)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.8)
    ax2.set_title('Residuals')
    ax2.set_ylabel('Residuals')
    ax2.grid(True, alpha=0.3)

    # 3. Residuals Distribution
    ax3 = plt.subplot(3, 3, 3)
    residuals.hist(ax=ax3, bins=30, alpha=0.7, color='purple', density=True)
    ax3.set_title('Residuals Distribution')
    ax3.set_xlabel('Residuals')
    ax3.set_ylabel('Density')
    ax3.grid(True, alpha=0.3)

    # 4. Q-Q Plot for Residuals
    ax4 = plt.subplot(3, 3, 4)
    from scipy import stats
    stats.probplot(residuals.dropna(), dist="norm", plot=ax4)
    ax4.set_title('Q-Q Plot (Residuals)')
    ax4.grid(True, alpha=0.3)

    # 5. ACF Plot
    ax5 = plt.subplot(3, 3, 5)
    from statsmodels.tsa.stattools import acf
    from statsmodels.graphics.tsaplots import plot_acf
    plot_acf(sales_clean, ax=ax5, lags=20, alpha=0.05)
    ax5.set_title('Autocorrelation Function')

    # 6. PACF Plot
    ax6 = plt.subplot(3, 3, 6)
    from statsmodels.graphics.tsaplots import plot_pacf
    plot_pacf(sales_clean, ax=ax6, lags=20, alpha=0.05)
    ax6.set_title('Partial Autocorrelation Function')

    # 7. Fitted vs Actual
    ax7 = plt.subplot(3, 3, 7)
    fitted_values = fitted_model.fittedvalues
    actual_values = sales_clean.loc[fitted_values.index]

    ax7.scatter(actual_values, fitted_values, alpha=0.6, color='orange')
    min_val = min(actual_values.min(), fitted_values.min())
    max_val = max(actual_values.max(), fitted_values.max())
    ax7.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
    ax7.set_xlabel('Actual Sales')
    ax7.set_ylabel('Fitted Sales')
    ax7.set_title('Fitted vs Actual')
    ax7.grid(True, alpha=0.3)

    # 8. Model Performance Metrics
    ax8 = plt.subplot(3, 3, 8)
    ax8.axis('off')

    metrics_text = f"""
    MODEL PERFORMANCE METRICS

    Model Order: ARIMA{arima_results['model_order']}
    AIC: {arima_results['diagnostics']['aic']:.2f}
    BIC: {arima_results['diagnostics']['bic']:.2f}

    In-Sample Metrics:
    MAE: {arima_results['diagnostics']['mae']:.2f}
    RMSE: {arima_results['diagnostics']['rmse']:.2f}
    MAPE: {arima_results['diagnostics']['mape']:.2f}%

    Residual Tests:
    White Noise: {arima_results['diagnostics']['residuals_white_noise']}
    Normal: {arima_results['diagnostics']['residuals_normal']}

    Stationarity:
    ADF p-value: {arima_results['stationarity']['adf_pvalue']:.4f}
    Stationary: {arima_results['stationarity']['is_stationary']}
    """

    ax8.text(0.1, 0.9, metrics_text, transform=ax8.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

    # 9. Forecast Summary
    ax9 = plt.subplot(3, 3, 9)
    ax9.axis('off')

    forecast_text = f"""
    FORECAST SUMMARY

    Last Actual Value: {arima_results['last_actual_value']:.2f}

    7-Day Forecast:
    """

    for i, (date, value, lower, upper) in enumerate(zip(
        arima_results['forecast_dates'],
        arima_results['forecast'],
        arima_results['forecast_lower_ci'],
        arima_results['forecast_upper_ci']
    )):
        forecast_text += f"\n{date}: {value:.0f} [{lower:.0f}, {upper:.0f}]"

    forecast_text += f"""

    Trend: {arima_results['trend'].upper()}
    Magnitude: {arima_results['trend_magnitude_percent']:.1f}%
    """

    ax9.text(0.1, 0.9, forecast_text, transform=ax9.transAxes, fontsize=9,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()

    # Save the comprehensive plot
    plot_filename = f'{OUTPUT_DIR}/store_{store_id}_comprehensive_analysis.png'
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"Comprehensive analysis plot saved: {plot_filename}")

    plt.show()

    return arima_results


def test_multiple_stores(store_ids: List[int] = [1, 2, 3, 4, 5]) -> Dict:
    """Test ARIMA analysis on multiple stores and create comparison plots."""

    print(f"\n{'='*60}")
    print(f"MULTI-STORE ARIMA COMPARISON")
    print(f"{'='*60}")

    all_results = {}
    comparison_data = []

    for store_id in store_ids:
        try:
            print(f"\nAnalyzing Store {store_id}...")
            results = create_comprehensive_plots(store_id)
            if results:
                all_results[store_id] = results
                comparison_data.append({
                    'Store': store_id,
                    'Model_Order': str(results['model_order']),
                    'AIC': results['diagnostics']['aic'],
                    'RMSE': results['diagnostics']['rmse'],
                    'MAPE': results['diagnostics']['mape'],
                    'Trend': results['trend'],
                    'Trend_Magnitude': results['trend_magnitude_percent']
                })
        except Exception as e:
            print(f"Error analyzing Store {store_id}: {e}")

    # Create comparison plots
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # AIC comparison
        df_comparison.plot(x='Store', y='AIC', kind='bar', ax=axes[0,0], color='skyblue')
        axes[0,0].set_title('AIC Comparison Across Stores')
        axes[0,0].set_ylabel('AIC')

        # RMSE comparison
        df_comparison.plot(x='Store', y='RMSE', kind='bar', ax=axes[0,1], color='lightcoral')
        axes[0,1].set_title('RMSE Comparison Across Stores')
        axes[0,1].set_ylabel('RMSE')

        # MAPE comparison
        df_comparison.plot(x='Store', y='MAPE', kind='bar', ax=axes[1,0], color='lightgreen')
        axes[1,0].set_title('MAPE Comparison Across Stores')
        axes[1,0].set_ylabel('MAPE (%)')

        # Trend magnitude comparison
        df_comparison.plot(x='Store', y='Trend_Magnitude', kind='bar', ax=axes[1,1], color='gold')
        axes[1,1].set_title('Trend Magnitude Comparison')
        axes[1,1].set_ylabel('Trend Magnitude (%)')

        plt.tight_layout()

        comparison_filename = f'{OUTPUT_DIR}/multi_store_comparison.png'
        plt.savefig(comparison_filename, dpi=300, bbox_inches='tight')
        print(f"\nMulti-store comparison plot saved: {comparison_filename}")

        plt.show()

        # Save comparison results
        comparison_results_file = f'{RESULTS_DIR}/multi_store_comparison.json'
        with open(comparison_results_file, 'w') as f:
            json.dump(all_results, f, indent=2)
        print(f"Multi-store results saved: {comparison_results_file}")

        # Print summary table
        print(f"\n{'='*80}")
        print("MULTI-STORE PERFORMANCE SUMMARY")
        print(f"{'='*80}")
        print(df_comparison.to_string(index=False))

    return all_results


if __name__ == "__main__":
    print("Starting Comprehensive ARIMA Testing and Visualization...")
    print(f"Output directory: {OUTPUT_DIR}")
    print(f"Results directory: {RESULTS_DIR}")

    # Test individual store (detailed analysis)
    print("\n" + "="*60)
    print("PHASE 1: DETAILED SINGLE STORE ANALYSIS")
    print("="*60)

    single_store_results = create_comprehensive_plots(store_id=1)

    # Test multiple stores (comparison analysis)
    print("\n" + "="*60)
    print("PHASE 2: MULTI-STORE COMPARISON ANALYSIS")
    print("="*60)

    multi_store_results = test_multiple_stores([1, 2, 3, 4, 5])

    print(f"\n{'='*60}")
    print("ARIMA TESTING COMPLETED SUCCESSFULLY!")
    print(f"{'='*60}")
    print(f"All plots saved to: {OUTPUT_DIR}")
    print(f"All results saved to: {RESULTS_DIR}")
    print(f"Total stores analyzed: {len(multi_store_results)}")
